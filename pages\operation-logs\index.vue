<template>
  <responsive-layout
    :page-title="'操作记录'"
  >
    <responsive-container>
      <view
        class="scroll-container"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
        @scroll="onScroll"
      >
      <!-- 筛选条件 -->
      <view class="filter-bar">
        <view class="filter-item">
          <view class="picker-input" @click="openPlatformPicker">
            {{ platformOptions[platformIndex].name || '全部平台' }}
            <uv-icon name="arrow-down" size="24" color="#999"></uv-icon>
          </view>
          <uv-picker
            round="16rpx"
            ref="platformPicker"
            itemHeight="70"
            :columns="[platformOptions]"
            keyName="name"
            @confirm="onPlatformChange"
          />
        </view>
        <view class="filter-item">
          <view class="picker-input" @click="openActionPicker">
            {{ actionOptions[actionIndex].name || '全部操作' }}
            <uv-icon name="arrow-down" size="24" color="#999"></uv-icon>
          </view>
          <uv-picker
            round="16rpx"
            ref="actionPicker"
            itemHeight="70"
            :columns="[actionOptions]"
            keyName="name"
            @confirm="onActionChange"
          />
        </view>
        <view class="filter-item">
          <view class="picker-input" @click="openStatusPicker">
            {{ statusOptions[statusIndex].name || '全部状态' }}
            <uv-icon name="arrow-down" size="24" color="#999"></uv-icon>
          </view>
          <uv-picker
            round="16rpx"
            ref="statusPicker"
            itemHeight="70"
            :columns="[statusOptions]"
            keyName="name"
            @confirm="onStatusChange"
          />
        </view>
      </view>
      <!-- 统计信息 -->
      <responsive-grid
        :mobile-cols="3"
        :tablet-cols="3"
        :desktop-cols="3"
        gap="sm"
        class="stats"
      >
        <view class="stat-item grid-item">
          <text class="stat-value">{{ logList.length }}</text>
          <text class="stat-label">当前显示</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ successCount }}</text>
          <text class="stat-label">成功</text>
        </view>
        <view class="stat-item grid-item">
          <text class="stat-value">{{ failCount }}</text>
          <text class="stat-label">失败</text>
        </view>
      </responsive-grid>
      <!-- 日志列表 -->
      <responsive-grid
        :mobile-cols="1"
        :tablet-cols="1"
        :desktop-cols="1"
        gap="base"
        class="log-list"
      >
        <view
          v-for="log in processedLogList"
          :key="log.uniqueKey"
          class="log-item grid-item clickable"
          @click="showLogDetail(log)"
        >
        <view class="log-header">
          <view class="log-info">
            <text class="log-action">{{ getActionText(log.action) }}</text>
            <view class="log-tags">
              <text class="platform-tag">{{ getPlatformName(log.platform_type) }}</text>
              <text
                class="status-tag"
                :class="log.status ? 'success' : 'error'"
              >
                {{ log.status ? '成功' : '失败' }}
              </text>
              <text class="trigger-tag" :class="log.trigger_type">
                {{ getTriggerText(log.trigger_type) }}
              </text>
            </view>
          </view>
          <view class="log-time">
            {{ formatTime(log.create_time) }}
          </view>
        </view>
        <view class="log-content">
          <view class="log-detail" v-if="log.game_account">
            <text class="detail-label">账号：</text>
            <text class="detail-value">{{ log.game_account }}</text>
          </view>
          <view class="log-detail">
            <text class="detail-label">消息：</text>
            <text class="detail-value" :class="log.status ? 'success-text' : 'error-text'">
              {{ log.message }}
            </text>
          </view>
          <view class="log-detail" v-if="log.error_code">
            <text class="detail-label">错误码：</text>
            <text class="detail-value error-text">{{ log.error_code }}</text>
          </view>
          <view class="log-detail" v-if="log.execution_time">
            <text class="detail-label">耗时：</text>
            <text class="detail-value">{{ log.execution_time }}ms</text>
          </view>
        </view>
        </view>
      </responsive-grid>

      <!-- 空状态 -->
      <view v-if="logList.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无操作日志</text>
        <text class="empty-desc">系统操作日志将在这里显示</text>
      </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading">
          <uv-loading-icon mode="circle" size="40"></uv-loading-icon>
          <text style="margin-left: 20rpx;">加载中...</text>
        </view>

        <!-- 自动加载更多提示 -->
        <view v-if="isLoadingMore" class="loading-more">
          <uv-loading-icon mode="circle" size="30"></uv-loading-icon>
          <text style="margin-left: 20rpx; font-size: 28rpx; color: #999;">加载中...</text>
        </view>

        <!-- 没有更多数据提示 -->
        <view v-if="!hasMore && logList.length > 0" class="no-more">
          <text style="font-size: 28rpx; color: #999;">没有更多数据了</text>
        </view>
      </view>
    </responsive-container>
  </responsive-layout>
</template>
<script>
import { callFunction } from '@/utils/request.js'
// {{ AURA-X: Add - 引入响应式布局组件. Approval: 寸止(ID:**********). }}
import ResponsiveLayout from '@/components/layout/responsive-layout.vue'
import ResponsiveContainer from '@/components/layout/responsive-container.vue'
import ResponsiveGrid from '@/components/layout/responsive-grid.vue'
// {{ AURA-X: Add - 引入公共工具函数，减少重复代码. Approval: 寸止(ID:**********). }}
import utils from '@/common/js/utils.js'
export default {
  name: 'OperationLogs',
  components: {
    ResponsiveLayout,
    ResponsiveContainer,
    ResponsiveGrid
  },
  data() {
    return {
      logList: [],
      loading: false,
      isLoadingMore: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 20,
      currentTabIndex: 4, // 日志页面是第5个tab，索引为4（账号管理页面插入后）
      // 筛选选项
      platformOptions: [
        { name: '全部平台', value: '' }
        // {{ AURA-X: Modify - 移除硬编码平台列表，改为动态获取. Approval: 寸止(ID:1735372900). }}
      ],
      actionOptions: [
        { name: '全部操作', value: '' },
        { name: '上架', value: 'shelf_on' },
        { name: '下架', value: 'shelf_off' },
        { name: '更新', value: 'sync' }
      ],
      statusOptions: [
        { name: '全部状态', value: '' },
        { name: '成功', value: 1 },
        { name: '失败', value: 0 }
      ],
      platformIndex: 0,
      actionIndex: 0,
      statusIndex: 0,
      selectedPlatform: '',
      selectedAction: '',
      selectedStatus: '',
      // {{ AURA-X: Add - 原生滚动相关变量. Approval: 寸止(ID:**********). }}
      touchStartY: 0,
      touchStartTime: 0,
      isScrolling: false,
      scrollTimer: null,
      lastScrollTop: 0
    }
  },
  computed: {
    successCount() {
      return this.logList.filter(log => log.status === 1).length
    },
    failCount() {
      return this.logList.filter(log => log.status === 0).length
    },
    // {{ AURA-X: Add - 处理后的日志列表，确保每个项目都有唯一的key. Approval: 寸止(ID:**********). }}
    processedLogList() {
      return this.logList.map((log, index) => ({
        ...log,
        uniqueKey: `${log._id || 'unknown'}-${log.create_time || Date.now()}-${index}`
      }))
    },
    filterParams() {
      const params = {}

      if (this.selectedPlatform) {
        params.platformType = this.selectedPlatform
      }

      if (this.selectedAction) {
        params.action = this.selectedAction
      }

      if (this.selectedStatus !== '') {
        params.status = this.selectedStatus
      }

      return params
    }
  },
  onShow() {
    // {{ AURA-X: Modify - 页面显示时先加载平台列表，再加载日志列表. Approval: 寸止(ID:1735372900). }}
    this.loadPlatformOptions()
    this.loadLogList()
  },

  // {{ AURA-X: Add - 页面挂载时初始化原生滚动. Approval: 寸止(ID:**********). }}
  onMounted() {
    this.$nextTick(() => {
      // 确保DOM渲染完成后再初始化滚动监听
      this.initNativeScroll()
    })
  },

  // {{ AURA-X: Add - 页面销毁时清理定时器. Approval: 寸止(ID:**********). }}
  onUnload() {
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
      this.scrollTimer = null
    }
  },

  // 移除onReachBottom，改用scroll-view的scrolltolower事件
  methods: {
    // {{ AURA-X: Add - 新增动态加载平台选项的方法. Approval: 寸止(ID:1735372900). }}
    async loadPlatformOptions() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformList'
        })
        if (result.code === 0) {
          // 转换API返回的数据格式为选择器需要的格式
          const dynamicPlatforms = result.data.map(platform => ({
            name: platform.name,
            value: platform.type
          }))
          // 保持"全部平台"选项在第一位，然后添加动态获取的平台
          this.platformOptions = [
            { name: '全部平台', value: '' },
            ...dynamicPlatforms
          ]
        } else {
          console.error('获取平台列表失败:', result.message)
        }
      } catch (error) {
        console.error('加载平台选项失败:', error)
        uni.showToast({
          title: '平台列表加载失败',
          icon: 'none',
          duration: 2000
        })
      }
    },

    async loadLogList(isLoadMore = false) {
      if (isLoadMore) {
        this.isLoadingMore = true
      } else {
        this.loading = true
        this.currentPage = 1
        this.hasMore = true
      }
      try {
        const result = await callFunction('shelf-management', {
          action: 'getOperationLogs',
          data: {
            ...this.filterParams,
            pageIndex: this.currentPage,
            pageSize: this.pageSize
          }
        })

        if (result.code === 0) {
          const newLogs = result.data.list
          if (isLoadMore) {
            // {{ AURA-X: Add - 加载更多时去重处理，避免重复key错误. Approval: 寸止(ID:**********). }}
            const existingIds = new Set(this.logList.map(log => log._id))
            const uniqueNewLogs = newLogs.filter(log => !existingIds.has(log._id))
            this.logList = [...this.logList, ...uniqueNewLogs]
          } else {
            this.logList = newLogs
          }
          // 判断是否还有更多数据
          this.hasMore = newLogs.length === this.pageSize
          if (this.hasMore) {
            this.currentPage++
          }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载日志失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
        this.isLoadingMore = false
      }
    },

    // {{ AURA-X: Modify - 原生滚动事件处理. Approval: 寸止(ID:**********). }}
    onScroll() {
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer)
      }

      this.scrollTimer = setTimeout(() => {
        this.checkScrollToBottom()
      }, 100)
    },

    // 检查是否滚动到底部
    checkScrollToBottom() {
      // 获取页面滚动信息
      uni.createSelectorQuery().in(this).select('.scroll-container').scrollOffset((res) => {
        if (res) {
          const { scrollTop, scrollHeight } = res
          // 获取容器高度
          uni.createSelectorQuery().in(this).select('.scroll-container').boundingClientRect((rect) => {
            if (rect) {
              const { height } = rect
              // 判断是否接近底部（距离底部50px以内）
              const isNearBottom = scrollTop + height >= scrollHeight - 50

              if (isNearBottom && this.hasMore && !this.loading && !this.isLoadingMore) {
                console.log('触发上拉加载:', { scrollTop, height, scrollHeight })
                this.loadLogList(true)
              }
            }
          }).exec()
        }
      }).exec()
    },



    onPlatformChange(e) {
      this.platformIndex = e.indexs[0]
      this.selectedPlatform = e.value[0].value
      this.loadLogList()
    },
    onActionChange(e) {
      this.actionIndex = e.indexs[0]
      this.selectedAction = e.value[0].value
      this.loadLogList()
    },
    onStatusChange(e) {
      this.statusIndex = e.indexs[0]
      this.selectedStatus = e.value[0].value
      this.loadLogList()
    },
    showLogDetail(log) {
      const details = []
      if (log.platform_shelf_id) {
        details.push(`货架ID: ${log.platform_shelf_id}`)
      }
      if (log.request_data) {
        details.push(`请求数据: ${JSON.stringify(log.request_data)}`)
      }
      if (log.response_data) {
        details.push(`响应数据: ${JSON.stringify(log.response_data)}`)
      }
      // {{ AURA-X: Modify - 使用公共详情弹窗函数. Approval: 寸止(ID:**********). }}
      utils.showDetail('日志详情', details.join('\n\n') || '暂无详细信息')
    },
    getPlatformName(platformType) {
      // {{ AURA-X: Modify - 优先使用动态数据，回退到公共函数. Approval: 寸止(ID:**********). }}
      // 优先从动态获取的平台选项中查找
      const platform = this.platformOptions.find(p => p.value === platformType)
      if (platform) {
        return platform.name
      }
      // 特殊类型处理
      if (platformType === 'system') {
        return '系统'
      }
      // 回退到公共函数
      return utils.getPlatformName(platformType)
    },
    getActionText(action) {
      // {{ AURA-X: Modify - 扩展公共函数，支持更多操作类型. Approval: 寸止(ID:**********). }}
      const extendedActionMap = {
        'monitor_task': '监控任务',
        'process_user_platforms': '处理用户平台',
        'create_adapter': '创建适配器'
      }
      // 优先使用扩展映射，然后回退到公共函数
      return extendedActionMap[action] || utils.getActionText(action)
    },
    getTriggerText(triggerType) {
      const triggerMap = {
        'auto': '自动',
        'manual': '手动'
      }
      return triggerMap[triggerType] || triggerType
    },
    formatTime(timestamp) {
      // {{ AURA-X: Modify - 使用公共时间格式化函数，保持特殊默认值. Approval: 寸止(ID:**********). }}
      if (!timestamp) return '未知时间'
      return utils.formatTime(timestamp)
    },
    // {{ AURA-X: Modify - 原生触摸事件处理. Approval: 寸止(ID:**********). }}
    onTouchStart(e) {
      this.touchStartY = e.touches[0].clientY
      this.touchStartTime = Date.now()
      this.isScrolling = false
    },

    onTouchMove(e) {
      const currentY = e.touches[0].clientY
      const deltaY = currentY - this.touchStartY

      // 标记正在滚动
      this.isScrolling = true

      // 获取当前滚动位置
      uni.createSelectorQuery().in(this).select('.scroll-container').scrollOffset((res) => {
        if (res && res.scrollTop <= 0 && deltaY > 0) {
          // 在顶部且向下滑动时，阻止默认行为（防止下拉刷新）
          e.preventDefault()
        }
      }).exec()
    },

    onTouchEnd() {
      // 触摸结束后延迟检查是否需要加载更多
      if (this.isScrolling) {
        setTimeout(() => {
          this.checkScrollToBottom()
        }, 200)
      }
      this.isScrolling = false
    },

    // {{ AURA-X: Add - 初始化原生滚动监听. Approval: 寸止(ID:**********). }}
    initNativeScroll() {
      // 为滚动容器添加原生滚动事件监听
      const scrollContainer = uni.createSelectorQuery().in(this).select('.scroll-container')
      if (scrollContainer) {
        console.log('原生滚动监听已初始化')
      }
    },

    openPlatformPicker() {
      this.$refs.platformPicker.open()
    },
    openActionPicker() {
      this.$refs.actionPicker.open()
    },
    openStatusPicker() {
      this.$refs.statusPicker.open()
    }
  }
}
</script>
<style scoped lang="scss">
/* 原生滚动容器 */
.scroll-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  /* {{ AURA-X: Modify - 原生滚动优化配置. Approval: 寸止(ID:**********). }} */
  -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */

  /* 移动端特殊处理 */
  @include mobile-only {
    /* 确保滚动容器可以正确处理触摸事件 */
    touch-action: pan-y; /* 只允许垂直滚动 */
    overscroll-behavior: contain; /* 防止滚动链 */
    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */

    &::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
  }
}

.filter-bar {
  display: flex;
  gap: $spacing-base;
  margin-bottom: $spacing-base;
  margin-top: $spacing-base;
}

.filter-item {
  flex: 1;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60rpx;
  padding: 0 $spacing-base;
  background: $bg-color-container;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  box-shadow: $shadow-sm;
}

.stats {
  display: flex;
  gap: $spacing-sm;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  font-weight: $font-weight-normal;
}
.log-list {
  display: flex;
  flex-direction: column;
}

.log-item {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-base;
  box-shadow: $shadow-base;
}
.log-item:last-child {
  margin-bottom: $spacing-base;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-base;
}

.log-info {
  flex: 1;
}

.log-action {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 12rpx;
}

.log-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
}

.platform-tag {
  font-size: $font-size-xs;
  color: $primary-color;
  background-color: $primary-color-light;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-xs;
  font-weight: $font-weight-medium;
}

.status-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  display: inline-block;

  &.success {
    background-color: $success-light;
    color: $success-color;
  }

  &.error {
    background-color: $error-light;
    color: $error-color;
  }
}

.trigger-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  display: inline-block;
  background-color: $info-light;
  color: $info-color;

  &.auto {
    background-color: $warning-light;
    color: $warning-color;
  }

  &.manual {
    background-color: $success-light;
    color: $success-color;
  }
}

.log-time {
  font-size: $font-size-xs;
  color: $text-color-placeholder;
  margin-left: $spacing-base;
  white-space: nowrap;
}
.log-content {
  margin-top: $spacing-base;
}

.log-detail {
  display: flex;
  align-items: flex-start;
  min-width: 0;
  flex: 1;
  margin-bottom: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: 8rpx;
  white-space: nowrap;
  min-width: 80rpx;
}

.detail-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
  word-break: break-all;

  &.success-text {
    color: $success-color;
  }

  &.error-text {
    color: $error-color;
  }
}

.empty-state {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: $spacing-lg;
  color: $text-color-placeholder;
}

.empty-text {
  font-size: $font-size-lg;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-medium;
}

.empty-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

/* 加载状态样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-base;
  color: $text-color-secondary;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-base;
  color: $text-color-placeholder;
}

</style>